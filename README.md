# FiendishFinder - Tibia Minimap Viewer & Stitcher

A comprehensive system for viewing and stitching Tibia minimap images. Features a PyQt6-based minimap viewer for exploring processed floor maps and a powerful stitching system for processing raw minimap tiles.

## Overview

FiendishFinder consists of two main components:

1. **Minimap Viewer**: A PyQt6-based GUI application for viewing and exploring processed minimap images with zoom, pan, and floor navigation capabilities.

2. **Minimap Stitcher**: A powerful tool that takes fragmented minimap tiles from the Tibia game and reconstructs them into complete, seamless minimap images for each floor level.

## Features

### Minimap Viewer
- **Interactive GUI**: PyQt6-based interface with smooth zoom and pan
- **Floor Navigation**: Easy switching between floors with preserved camera position
- **Zoom Controls**: Mouse wheel zoom and zoom slider with fit-to-window option
- **Status Information**: Real-time display of current floor and zoom level

### Minimap Stitcher
- **Automatic Tile Analysis**: Parses filename patterns to extract coordinates and floor levels
- **Spatial Relationship Detection**: Determines correct positioning based on world coordinates
- **Seamless Stitching**: Combines 256x256 pixel tiles into complete floor maps
- **Missing Tile Handling**: Grace<PERSON> handles gaps in tile coverage
- **Multiple Output Formats**: Supports PNG and JPEG output formats
- **Progress Tracking**: Comprehensive logging and progress reporting
- **Validation**: Detects and reports missing tiles and other issues
- **Batch Processing**: Process all floors at once or individual floors

## File Structure

```
FiendishFinder/
├── main.py                # Main entry point - launches minimap viewer
├── stitch_minimap.py      # Standalone stitching script
├── minimap_viewer.py      # PyQt6 minimap viewer component
├── minimap_stitcher.py    # Core stitching system
├── minimap_demo.py        # Demo application with additional features
├── run_minimap_viewer.py  # Alternative launcher with options
├── raw_minimap/           # Input directory for tile images
│   └── Minimap_Color_X_Y_Z.png  # Individual tiles
└── processed_minimap/     # Output directory for stitched images
    ├── floor_00.png       # Complete floor maps
    ├── floor_01.png
    ├── ...
    └── stitching_report.txt
```

## Tile Naming Convention

Input tiles must follow this naming pattern:
```
Minimap_Color_X_Y_Z.png
```

Where:
- `X`: World X coordinate (e.g., 31744)
- `Y`: World Y coordinate (e.g., 30976) 
- `Z`: Floor level (0-15)

Each tile represents a 256x256 pixel area of the game world.

## Quick Start

### 1. View Existing Minimap Images

If you already have processed minimap images in the `processed_minimap` directory:

```bash
python main.py
```

This launches the interactive minimap viewer where you can:
- Navigate between floors using the dropdown or arrow keys
- Zoom in/out with mouse wheel or zoom controls
- Pan around by dragging with the mouse
- Use "Fit to Window" to see the entire floor

### 2. Process Raw Minimap Tiles

If you have raw minimap tiles that need to be stitched together:

```bash
# Process all floors
python stitch_minimap.py

# Process a specific floor
python stitch_minimap.py --floor 7

# Use custom directories
python stitch_minimap.py --input my_tiles --output my_maps

# Output as JPEG instead of PNG
python stitch_minimap.py --format JPEG

# Enable verbose output
python stitch_minimap.py --verbose
```

### 3. Alternative Launchers

```bash
# Run with dependency checking
python run_minimap_viewer.py

# Run the demo with additional features
python run_minimap_viewer.py --demo
```

## Usage Details

### Minimap Viewer Controls

- **Mouse Wheel**: Zoom in/out
- **Left Click + Drag**: Pan around the map
- **Floor Dropdown**: Select different floors
- **Arrow Keys**: Navigate between floors
- **Zoom Slider**: Fine-tune zoom level
- **Fit to Window**: Reset view to show entire floor

### Stitching Command Line Options

```bash
python stitch_minimap.py [OPTIONS]

Options:
  --floor FLOOR         Process only the specified floor (0-15)
  --format {PNG,JPEG}   Output image format (default: PNG)
  --input INPUT         Input directory for raw tiles (default: raw_minimap)
  --output OUTPUT       Output directory for processed images (default: processed_minimap)
  --verbose, -v         Enable verbose output
  --help               Show help message
```

### Python API

```python
from minimap_stitcher import MinimapStitchingSystem

# Initialize the system
system = MinimapStitchingSystem("raw_minimap", "processed_minimap")

# Process all floors
summary = system.process_all_floors('PNG')

# Process a single floor
output_path = system.process_single_floor(7, 'PNG')

# Get available floors
floors = system.get_available_floors()
```

## Output

The system generates:

1. **Floor Images**: Complete minimap images for each floor
   - Format: `floor_XX.png` (where XX is the floor number)
   - Dimensions: Varies based on tile coverage (typically 2304x2048 or 2560x2048 pixels)

2. **Summary Report**: Detailed text report with processing statistics
   - File: `stitching_report.txt`
   - Contains tile counts, dimensions, and success status for each floor

## System Components

### MinimapAnalyzer
- Scans and parses tile files
- Builds floor map structures
- Validates tile coverage and detects missing tiles

### SpatialAnalyzer
- Converts world coordinates to grid positions
- Calculates canvas sizes and tile positions
- Handles coordinate system mapping

### ImageStitcher
- Performs the actual image stitching
- Handles different image formats and transparency
- Manages output file generation

### OverlapHandler
- Detects overlapping tiles (rare in Tibia's grid system)
- Provides blending capabilities for overlapping areas

### MinimapStitchingSystem
- Main orchestrator that coordinates all components
- Provides high-level API for processing
- Generates summary reports

## Requirements

- Python 3.7+
- Pillow (PIL) for image processing
- NumPy for array operations

Install dependencies:
```bash
pip install Pillow numpy
```

## Performance

- Processing time: ~3-5 seconds per floor (depending on tile count)
- Memory usage: Moderate (loads one tile at a time)
- Output file sizes: 36KB - 830KB per floor (varies by content complexity)

## Validation and Error Handling

The system includes comprehensive validation:

- **Missing Tiles**: Reports gaps in tile coverage
- **Invalid Filenames**: Warns about incorrectly named files
- **Corrupted Images**: Handles damaged or unreadable tiles
- **Coordinate Validation**: Ensures tiles fit within expected bounds

## Example Results

From the test dataset:
- 16 floors processed successfully
- 1,069 total tiles processed
- Floor dimensions range from 2304x2048 to 2560x2048 pixels
- Processing completed in under 10 seconds

## Troubleshooting

**No tiles found**: Check that tile files are in the correct directory and follow the naming convention.

**Missing tiles warning**: Some areas of the map may not have been explored or captured. The system will still generate complete images with transparent areas for missing tiles.

**Memory issues**: For very large datasets, consider processing floors individually using the `--floor` parameter.

## License

This project is provided as-is for educational and personal use with Tibia minimap data.
